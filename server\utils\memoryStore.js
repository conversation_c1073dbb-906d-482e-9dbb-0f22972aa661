// Simple in-memory storage for development when MongoDB is not available
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

class MemoryStore {
  constructor() {
    this.users = new Map();
    this.invoices = new Map();
    this.quotes = new Map();
    this.userIdCounter = 1;
    this.invoiceIdCounter = 1;
    this.quoteIdCounter = 1;
  }

  // User methods
  async createUser(userData) {
    const { name, email, password } = userData;
    
    // Check if user already exists
    for (const [id, user] of this.users) {
      if (user.email === email.toLowerCase()) {
        throw new Error('User already exists');
      }
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    const user = {
      id: this.userIdCounter++,
      name,
      email: email.toLowerCase(),
      password: hashedPassword,
      createdAt: new Date(),
      isActive: true
    };

    this.users.set(user.id, user);
    return { ...user, password: undefined }; // Don't return password
  }

  async findUserByEmail(email) {
    for (const [id, user] of this.users) {
      if (user.email === email.toLowerCase()) {
        return user;
      }
    }
    return null;
  }

  async findUserById(id) {
    return this.users.get(parseInt(id)) || null;
  }

  async validatePassword(plainPassword, hashedPassword) {
    return await bcrypt.compare(plainPassword, hashedPassword);
  }

  generateToken(userId) {
    return jwt.sign(
      { user: { id: userId } },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );
  }

  // Invoice methods
  createInvoice(invoiceData) {
    const invoice = {
      id: this.invoiceIdCounter++,
      ...invoiceData,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    this.invoices.set(invoice.id, invoice);
    return invoice;
  }

  getInvoicesByUser(userId) {
    const userInvoices = [];
    for (const [id, invoice] of this.invoices) {
      if (invoice.user === parseInt(userId)) {
        userInvoices.push(invoice);
      }
    }
    return userInvoices.sort((a, b) => b.createdAt - a.createdAt);
  }

  getInvoiceById(id, userId) {
    const invoice = this.invoices.get(parseInt(id));
    if (invoice && invoice.user === parseInt(userId)) {
      return invoice;
    }
    return null;
  }

  // Quote methods
  createQuote(quoteData) {
    const quote = {
      id: this.quoteIdCounter++,
      ...quoteData,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    this.quotes.set(quote.id, quote);
    return quote;
  }

  getQuotesByUser(userId) {
    const userQuotes = [];
    for (const [id, quote] of this.quotes) {
      if (quote.user === parseInt(userId)) {
        userQuotes.push(quote);
      }
    }
    return userQuotes.sort((a, b) => b.createdAt - a.createdAt);
  }

  getQuoteById(id, userId) {
    const quote = this.quotes.get(parseInt(id));
    if (quote && quote.user === parseInt(userId)) {
      return quote;
    }
    return null;
  }

  // Get stats
  getStats() {
    return {
      users: this.users.size,
      invoices: this.invoices.size,
      quotes: this.quotes.size
    };
  }
}

module.exports = new MemoryStore();
