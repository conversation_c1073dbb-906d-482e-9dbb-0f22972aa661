{"name": "invquotemgr-server", "version": "1.0.0", "description": "Backend server for Invoice and Quote Management System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["express", "mongodb", "api", "invoice", "quote"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "dotenv": "^16.3.1", "cors": "^2.8.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-validator": "^7.0.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5"}, "devDependencies": {"nodemon": "^3.0.2"}}